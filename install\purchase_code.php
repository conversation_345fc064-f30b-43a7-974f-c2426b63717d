<?php 
ob_start();
session_start();
define('START', true);
include ("_init.php");

$json = array();

if (defined('INSTALLED')) {
	if (is_ajax()) {
		$json['redirect'] = root_url().'index.php';
		echo json_encode($json);
		exit();
	} else {
		header('Location: ../index.php');
	}
}

// تم تخطي التحقق من الخوادم الخارجية
// if(!checkValidationServerConnection() || !checkEnvatoServerConnection()) {
//	if (is_ajax()) {
//		$json['redirect'] = root_url().'install/index.php';
//		echo json_encode($json);
//		exit();
//	} else {
//		redirect('index.php');
//	}
// }

$errors = array();
$success = array();
$info = array();

$errors['internet_connection'] = null;
$errors['purchase_username'] = null;
$errors['purchase_code'] = null;
$errors['config_error'] = null;

$ecnesil_path = DIR_INCLUDE.'config/purchase.php';
$config_path = ROOT . '/config.php';

if ($request->server['REQUEST_METHOD'] == 'POST')
{
	header('Content-Type: application/json');

	// تسجيل البيانات المستلمة
	error_log("POST data received: " . print_r($request->post, true));

	try {
		// إنشاء ملف الترخيص مباشرة بدون استدعاء وظائف معقدة
		$username = !empty($request->post['purchase_username']) ? $request->post['purchase_username'] : 'admin';
		$purchase_code = !empty($request->post['purchase_code']) ? $request->post['purchase_code'] : 'bypass-code-12345';

		// إنشاء محتوى ملف الترخيص
		$purchase_content = "<?php defined('ENVIRONMENT') OR exit('No direct access allowed!');\n";
		$purchase_content .= "return array('username'=>'".addslashes($username)."','purchase_code'=>'".addslashes($purchase_code)."');";

		// كتابة الملف
		if (file_put_contents($ecnesil_path, $purchase_content) !== false) {
			error_log("License file created successfully");
			$json['redirect'] = 'database.php';
		} else {
			error_log("Failed to create license file");
			$json['config_error'] = 'Could not write license file!';
		}
	} catch (Exception $e) {
		error_log("Exception: " . $e->getMessage());
		$json['preparation'] = 'Error: ' . $e->getMessage();
	}

	echo json_encode($json);
	exit();
}
?>

<?php 
$title = 'Validation-Modern POS';
include("header.php"); ?>
<?php include '../_inc/template/install/purchase_code.php'; ?>
<?php include("footer.php"); ?>
