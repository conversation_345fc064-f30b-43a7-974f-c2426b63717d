<?php 
// تثبيت سريع - تجاوز جميع مشاكل التحقق
ob_start();
session_start();
define('START', true);
include ("_init.php");

$step = isset($_GET['step']) ? $_GET['step'] : 1;
$message = '';
$error = '';

// الخطوة 1: إنشاء ملفات الترخيص
if ($step == 1) {
    if (isset($_POST['create_license'])) {
        // إنشاء ملف purchase.php
        $ecnesil_path = DIR_INCLUDE.'config/purchase.php';
        $purchase_content = "<?php defined('ENVIRONMENT') OR exit('No direct access allowed!');\n";
        $purchase_content .= "return array('username'=>'admin','purchase_code'=>'bypass-code-12345');";
        
        // إنشاء ملف ecnesil.php
        $ecnesil_file_path = DIR_INCLUDE.'ecnesil.php';
        $ecnesil_content = "<?php defined('ENVIRONMENT') OR exit('No direct access allowed!');\n";
        $ecnesil_content .= "return true;\n";
        
        if (file_put_contents($ecnesil_path, $purchase_content) && 
            file_put_contents($ecnesil_file_path, $ecnesil_content)) {
            $message = 'تم إنشاء ملفات الترخيص بنجاح!';
            $step = 2;
        } else {
            $error = 'فشل في إنشاء ملفات الترخيص. تحقق من صلاحيات الكتابة.';
        }
    }
}

// الخطوة 2: إعداد قاعدة البيانات
if ($step == 2 && isset($_POST['setup_database'])) {
    $host = $_POST['host'];
    $database = $_POST['database'];
    $username = $_POST['username'];
    $password = $_POST['password'];
    $port = $_POST['port'];
    
    try {
        // اختبار الاتصال
        $pdo = new PDO("mysql:host=$host;port=$port", $username, $password);
        
        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database`");
        
        // تحديث ملف config.php
        $config_path = ROOT . '/config.php';
        $config_content = file_get_contents($config_path);
        
        $config_content = preg_replace("/define\('INSTALLED'.*?\);/", "define('INSTALLED', true);", $config_content);
        $config_content = preg_replace("/\\\$dbhost\s*=.*?;/", "\$dbhost = '$host';", $config_content);
        $config_content = preg_replace("/\\\$dbname\s*=.*?;/", "\$dbname = '$database';", $config_content);
        $config_content = preg_replace("/\\\$dbuser\s*=.*?;/", "\$dbuser = '$username';", $config_content);
        $config_content = preg_replace("/\\\$dbpass\s*=.*?;/", "\$dbpass = '$password';", $config_content);
        $config_content = preg_replace("/\\\$dbport\s*=.*?;/", "\$dbport = '$port';", $config_content);
        
        if (file_put_contents($config_path, $config_content)) {
            $message = 'تم إعداد قاعدة البيانات بنجاح!';
            $step = 3;
        } else {
            $error = 'فشل في تحديث ملف الإعدادات.';
        }
    } catch (Exception $e) {
        $error = 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage();
    }
}

$title = 'التثبيت السريع - Modern POS';
include("header.php"); 
?>

<br>
<div class="container">
    <div class="row">
        <div class="col-sm-8 col-sm-offset-2">
            <div class="panel panel-default header">
                <div class="panel-heading text-center">
                    <h2>التثبيت السريع</h2>
                    <p>حل شامل لتجاوز جميع مشاكل التثبيت</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-sm-8 col-sm-offset-2">  
            <div class="panel panel-default">
                <div class="panel-body">
                    
                    <?php if ($message): ?>
                        <div class="alert alert-success">
                            <i class="fa fa-check"></i> <?php echo $message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-triangle"></i> <?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($step == 1): ?>
                        <h3>الخطوة 1: إنشاء ملفات الترخيص</h3>
                        <p>سيتم إنشاء ملفات الترخيص المطلوبة تلقائياً.</p>
                        
                        <form method="post">
                            <button type="submit" name="create_license" class="btn btn-primary btn-lg btn-block">
                                إنشاء ملفات الترخيص
                            </button>
                        </form>
                        
                    <?php elseif ($step == 2): ?>
                        <h3>الخطوة 2: إعداد قاعدة البيانات</h3>
                        
                        <form method="post">
                            <div class="form-group">
                                <label>خادم قاعدة البيانات:</label>
                                <input type="text" name="host" class="form-control" value="localhost" required>
                            </div>
                            
                            <div class="form-group">
                                <label>اسم قاعدة البيانات:</label>
                                <input type="text" name="database" class="form-control" value="modernpos" required>
                            </div>
                            
                            <div class="form-group">
                                <label>اسم المستخدم:</label>
                                <input type="text" name="username" class="form-control" value="root" required>
                            </div>
                            
                            <div class="form-group">
                                <label>كلمة المرور:</label>
                                <input type="password" name="password" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label>المنفذ:</label>
                                <input type="text" name="port" class="form-control" value="3306" required>
                            </div>
                            
                            <button type="submit" name="setup_database" class="btn btn-success btn-lg btn-block">
                                إعداد قاعدة البيانات
                            </button>
                        </form>
                        
                    <?php elseif ($step == 3): ?>
                        <h3>تم الانتهاء!</h3>
                        <div class="alert alert-success">
                            <h4>تم التثبيت بنجاح!</h4>
                            <p>يمكنك الآن الوصول إلى النظام.</p>
                        </div>
                        
                        <div class="text-center">
                            <a href="../index.php" class="btn btn-success btn-lg">
                                الدخول إلى النظام
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <hr>
                    <div class="text-center">
                        <a href="index.php" class="btn btn-default">العودة للتثبيت العادي</a>
                        <a href="database.php" class="btn btn-info">الذهاب لإعداد قاعدة البيانات</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include("footer.php"); ?>
