<?php 
// تثبيت مع تشخيص مفصل للأخطاء
ob_start();
session_start();
define('START', true);
include ("_init.php");

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>تشخيص التثبيت</h2>";

// فحص الصلاحيات
echo "<h3>1. فحص الصلاحيات:</h3>";
$config_dir = DIR_INCLUDE.'config/';
echo "مجلد الإعدادات: " . $config_dir . "<br>";
echo "قابل للكتابة: " . (is_writable($config_dir) ? "✅ نعم" : "❌ لا") . "<br>";

$config_file = ROOT . '/config.php';
echo "ملف الإعدادات: " . $config_file . "<br>";
echo "موجود: " . (file_exists($config_file) ? "✅ نعم" : "❌ لا") . "<br>";
echo "قابل للكتابة: " . (is_writable($config_file) ? "✅ نعم" : "❌ لا") . "<br>";

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    echo "<h3>2. بيانات النموذج المستلمة:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    if (isset($_POST['create_license'])) {
        echo "<h3>3. إنشاء ملفات الترخيص:</h3>";
        
        // إنشاء ملف purchase.php
        $ecnesil_path = DIR_INCLUDE.'config/purchase.php';
        $purchase_content = "<?php defined('ENVIRONMENT') OR exit('No direct access allowed!');\n";
        $purchase_content .= "return array('username'=>'admin','purchase_code'=>'bypass-code-12345');";
        
        echo "مسار ملف الترخيص: " . $ecnesil_path . "<br>";
        
        if (file_put_contents($ecnesil_path, $purchase_content)) {
            echo "✅ تم إنشاء ملف الترخيص بنجاح<br>";
        } else {
            echo "❌ فشل في إنشاء ملف الترخيص<br>";
        }
        
        // إنشاء ملف ecnesil.php
        $ecnesil_file_path = DIR_INCLUDE.'ecnesil.php';
        $ecnesil_content = "<?php defined('ENVIRONMENT') OR exit('No direct access allowed!');\n";
        $ecnesil_content .= "return true;\n";
        
        echo "مسار ملف التحقق: " . $ecnesil_file_path . "<br>";
        
        if (file_put_contents($ecnesil_file_path, $ecnesil_content)) {
            echo "✅ تم إنشاء ملف التحقق بنجاح<br>";
        } else {
            echo "❌ فشل في إنشاء ملف التحقق<br>";
        }
    }
    
    if (isset($_POST['setup_database'])) {
        echo "<h3>4. إعداد قاعدة البيانات:</h3>";
        
        $host = trim($_POST['host']);
        $database = trim($_POST['database']);
        $username = trim($_POST['username']);
        $password = trim($_POST['password']);
        $port = trim($_POST['port']);
        
        echo "الخادم: $host<br>";
        echo "قاعدة البيانات: $database<br>";
        echo "المستخدم: $username<br>";
        echo "المنفذ: $port<br>";
        
        try {
            echo "محاولة الاتصال...<br>";
            $pdo = new PDO("mysql:host=$host;port=$port", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            echo "✅ تم الاتصال بالخادم بنجاح<br>";
            
            echo "إنشاء قاعدة البيانات...<br>";
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8 COLLATE utf8_general_ci");
            echo "✅ تم إنشاء/التحقق من قاعدة البيانات<br>";
            
            echo "الاتصال بقاعدة البيانات...<br>";
            $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            echo "✅ تم الاتصال بقاعدة البيانات<br>";
            
            echo "إنشاء الجداول...<br>";
            
            // جدول المستخدمين
            $pdo->exec("CREATE TABLE IF NOT EXISTS `users` (
                `user_id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(255) NOT NULL,
                `email` varchar(255) NOT NULL,
                `password` varchar(255) NOT NULL,
                `user_group_id` int(11) NOT NULL DEFAULT 1,
                `status` tinyint(1) NOT NULL DEFAULT 1,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`user_id`),
                UNIQUE KEY `email` (`email`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8");
            echo "✅ تم إنشاء جدول المستخدمين<br>";
            
            // إدراج المستخدم الافتراضي
            $hashed_password = password_hash('password', PASSWORD_DEFAULT);
            $pdo->exec("INSERT IGNORE INTO `users` (`user_id`, `name`, `email`, `password`, `user_group_id`, `status`) VALUES
            (1, 'Administrator', '<EMAIL>', '$hashed_password', 1, 1)");
            echo "✅ تم إدراج المستخدم الافتراضي<br>";
            
            // جدول الإعدادات
            $pdo->exec("CREATE TABLE IF NOT EXISTS `settings` (
                `setting_id` int(11) NOT NULL AUTO_INCREMENT,
                `key` varchar(255) NOT NULL,
                `value` text,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`setting_id`),
                UNIQUE KEY `key` (`key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8");
            echo "✅ تم إنشاء جدول الإعدادات<br>";
            
            // إدراج الإعدادات الافتراضية
            $pdo->exec("INSERT IGNORE INTO `settings` (`key`, `value`) VALUES
            ('store_name', 'Modern POS'),
            ('store_email', '<EMAIL>'),
            ('currency', 'USD'),
            ('timezone', 'UTC'),
            ('version', '3.4')");
            echo "✅ تم إدراج الإعدادات الافتراضية<br>";
            
            echo "<h3>5. تحديث ملف الإعدادات:</h3>";
            
            $config_path = ROOT . '/config.php';
            $config_content = file_get_contents($config_path);
            
            echo "قراءة ملف الإعدادات: " . (strlen($config_content) > 0 ? "✅ نجح" : "❌ فشل") . "<br>";
            
            $config_content = preg_replace("/\\\$dbhost\s*=.*?;/", "\$dbhost = '$host';", $config_content);
            $config_content = preg_replace("/\\\$dbname\s*=.*?;/", "\$dbname = '$database';", $config_content);
            $config_content = preg_replace("/\\\$dbuser\s*=.*?;/", "\$dbuser = '$username';", $config_content);
            $config_content = preg_replace("/\\\$dbpass\s*=.*?;/", "\$dbpass = '$password';", $config_content);
            $config_content = preg_replace("/\\\$dbport\s*=.*?;/", "\$dbport = '$port';", $config_content);
            $config_content = preg_replace("/define\('INSTALLED'.*?\);/", "define('INSTALLED', true);", $config_content);
            
            if (file_put_contents($config_path, $config_content)) {
                echo "✅ تم تحديث ملف الإعدادات بنجاح<br>";
                echo "<h2 style='color: green;'>🎉 تم التثبيت بنجاح!</h2>";
                echo "<p><a href='../index.php' class='btn btn-success'>الدخول إلى النظام</a></p>";
                echo "<p><strong>بيانات الدخول:</strong><br>";
                echo "البريد: <EMAIL><br>";
                echo "كلمة المرور: password</p>";
            } else {
                echo "❌ فشل في تحديث ملف الإعدادات<br>";
            }
            
        } catch (Exception $e) {
            echo "❌ خطأ: " . $e->getMessage() . "<br>";
            echo "تفاصيل الخطأ:<br>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }
    }
}
?>

<h3>نموذج التثبيت:</h3>

<form method="post" style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">
    <h4>الخطوة 1: إنشاء ملفات الترخيص</h4>
    <button type="submit" name="create_license">إنشاء ملفات الترخيص</button>
</form>

<form method="post" style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">
    <h4>الخطوة 2: إعداد قاعدة البيانات</h4>
    <table>
        <tr>
            <td>الخادم:</td>
            <td><input type="text" name="host" value="localhost" required></td>
        </tr>
        <tr>
            <td>قاعدة البيانات:</td>
            <td><input type="text" name="database" value="modernpos" required></td>
        </tr>
        <tr>
            <td>المستخدم:</td>
            <td><input type="text" name="username" value="root" required></td>
        </tr>
        <tr>
            <td>كلمة المرور:</td>
            <td><input type="password" name="password"></td>
        </tr>
        <tr>
            <td>المنفذ:</td>
            <td><input type="text" name="port" value="3306" required></td>
        </tr>
        <tr>
            <td colspan="2"><button type="submit" name="setup_database">إعداد قاعدة البيانات</button></td>
        </tr>
    </table>
</form>

<style>
table { border-collapse: collapse; width: 100%; }
td { padding: 8px; border: 1px solid #ddd; }
input { width: 200px; padding: 5px; }
button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; }
button:hover { background: #005a87; }
.btn { display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; }
</style>
