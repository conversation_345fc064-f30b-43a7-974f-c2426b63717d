<?php
// ملف اختبار لتخطي التحقق من رمز الشراء
define('START', true);
include ("install/_init.php");

echo "<h1>اختبار تخطي التحقق من رمز الشراء</h1>";

// اختبار الوظائف
echo "<h2>اختبار الوظائف:</h2>";

echo "<p>checkValidationServerConnection(): " . (checkValidationServerConnection() ? "✅ نجح" : "❌ فشل") . "</p>";
echo "<p>checkEnvatoServerConnection(): " . (checkEnvatoServerConnection() ? "✅ نجح" : "❌ فشل") . "</p>";
echo "<p>revalidate_pcode(): " . revalidate_pcode() . "</p>";

// اختبار إنشاء ملف الترخيص
echo "<h2>اختبار إنشاء ملف الترخيص:</h2>";

$test_username = 'test_admin';
$test_purchase_code = 'test-bypass-12345';
$test_path = DIR_INCLUDE.'config/test_purchase.php';

try {
    // إنشاء محتوى ملف الترخيص
    $purchase_content = "<?php defined('ENVIRONMENT') OR exit('No direct access allowed!');\n";
    $purchase_content .= "return array('username'=>'".addslashes($test_username)."','purchase_code'=>'".addslashes($test_purchase_code)."');";
    
    // كتابة الملف
    if (file_put_contents($test_path, $purchase_content) !== false) {
        echo "<p>✅ تم إنشاء ملف الترخيص التجريبي بنجاح</p>";
        
        // قراءة الملف للتحقق
        if (file_exists($test_path)) {
            $test_data = include $test_path;
            echo "<p>محتوى الملف: " . print_r($test_data, true) . "</p>";
            
            // حذف الملف التجريبي
            unlink($test_path);
            echo "<p>✅ تم حذف الملف التجريبي</p>";
        }
    } else {
        echo "<p>❌ فشل في إنشاء ملف الترخيص التجريبي</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ خطأ: " . $e->getMessage() . "</p>";
}

// اختبار صلاحيات الكتابة
echo "<h2>اختبار صلاحيات الكتابة:</h2>";

$config_path = ROOT . '/config.php';
$ecnesil_path = DIR_INCLUDE.'config/purchase.php';

echo "<p>config.php writable: " . (is_writable($config_path) ? "✅ نعم" : "❌ لا") . "</p>";
echo "<p>config directory writable: " . (is_writable(dirname($config_path)) ? "✅ نعم" : "❌ لا") . "</p>";
echo "<p>purchase.php writable: " . (is_writable($ecnesil_path) ? "✅ نعم" : "❌ لا") . "</p>";
echo "<p>purchase config directory writable: " . (is_writable(dirname($ecnesil_path)) ? "✅ نعم" : "❌ لا") . "</p>";

echo "<h2>معلومات النظام:</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>ROOT: " . ROOT . "</p>";
echo "<p>DIR_INCLUDE: " . DIR_INCLUDE . "</p>";

echo "<hr>";
echo "<p><a href='install/purchase_code.php'>الذهاب إلى صفحة التحقق من رمز الشراء</a></p>";
echo "<p><a href='install/'>الذهاب إلى صفحة التثبيت</a></p>";
?>
