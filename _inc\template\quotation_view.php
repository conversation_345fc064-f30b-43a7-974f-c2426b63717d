<style>
@media only print {
	.print-modal-content {
	     position: absolute;
	     overflow: auto;
	     width: 100%;
	     height: 100%;
	     z-index: 100000; /* CSS doesn't support infinity */
	}
	.modal-dialog {
		display: none;
	}
}
</style>
<?php $customer_name = get_the_customer($quotation['customer_id'],'customer_name');

// Qrcode
$qrcode_text = 'Reference No.: ' . $quotation['reference_no'] . ', Customer: ' . $customer_name;
include(DIR_SRC.'/phpqrcode/qrlib.php');
QRcode::png($qrcode_text, ROOT.'/storage/qrcode.png', 'L', 3, 1);
?>
<div id="quotataion-view">
	<div class="logo center mb-10">
	<?php if ($store->get('logo')): ?>
	  <img src="<?php echo root_url(); ?>/assets/itsolution24/img/logo-favicons/<?php echo $store->get('logo'); ?>" width="auto" height="60" alt="logo">
	<?php else: ?>
	  <img src="<?php echo root_url(); ?>/assets/itsolution24/img/logo-favicons/nologo.png" width="auto" height="60" alt="logo">
	<?php endif; ?>
	</div>
	<table class="table table-bordered table-condensed">
		<tbody>
			<tr>
				<td class="w-50p va-t">
					<h6 class="italic bold"><?php echo trans('label_from');?>:</h6>
					<address class="mb-0">
						<h4 class="bold"><?php echo store('name');?></h4>
						<p><?php echo nl2br(store('address'));?></p>
						<?php if ($vat = store('vat_reg_no')):?>
						<span><?php echo trans('label_vat_number');?>: <?php echo $vat;?></span><br>
						<?php endif; ?>
						<?php if (get_preference('invoice_view') == 'indian_gst'):?>
				            <?php if (get_preference('gst_reg_no')):?>
				              <span><?php echo trans('label_gst_reg_no'); ?>: <?php echo get_preference('gst_reg_no'); ?></span>
				            <?php endif;?>
				        <?php endif;?>
						<span><?php echo trans('label_mobile');?>: <?php echo store('mobile');?></span><br>
						<span><?php echo trans('label_email');?>: <?php echo get_preference('smtp_username');?></span><br>
						<span><?php echo trans('label_date');?>: <?php echo format_date($quotation['created_at']);?></span><br>
						<span><?php echo trans('label_reference_no');?>: <?php echo $quotation['reference_no'];?></span><br>
					</address>
				</td>
				<td class="w-50p va-t">
					<h6 class="italic bold"><?php echo trans('label_to');?>:</h6>
					<address class="mb-0">
						<h4 class="bold"><?php echo $customer_name;?></h4>
						<p><?php echo nl2br(get_the_customer($quotation['customer_id'],'customer_address'));?></p>
						<span><?php echo trans('label_mobile');?>: <?php echo get_the_customer($quotation['customer_id'],'customer_mobile');?></span><br>
						<span><?php echo trans('label_email');?>: <?php echo get_the_customer($quotation['customer_id'],'customer_email');?></span><br>
						<div class="qrcode">
						  <img src="<?php echo root_url();?>storage/qrcode.png" alt="qr code">
						</div>
					</address>
				</td>
			</tr>
		</tbody>
	</table>

	<div class="xrow">
		<div class="xcol-md-12">
			<div class="table-responsive">
				<table class="table table-bordered margin-b0 table-condensed">
					<thead>
					<tr class="bg-gray">
						<td class="w-5 text-center">
							<?php echo trans('label_sl'); ?>	
						</td>
						<td class="w-35 text-center">
							<?php echo trans('label_product'); ?>	
						</td>
						<td class="w-20 text-center">
							<?php echo trans('label_unit_price'); ?>
						</td>
						<td class="w-15 text-center">
							<?php echo trans('label_item_tax'); ?>
						</td>
						<td class="w-25 text-center">
							<?php echo trans('label_subtotal'); ?>
						</td>
					</tr>
					</thead>
					<tbody>
						<?php $inc=1;foreach ($quotation_items as $product) : ?>
							<tr>
								<td class="text-center"><?php echo $inc;?></td>
								<td>
									<?php echo $product['item_name']; ?> (x<?php echo currency_format($product['item_quantity']); ?> <?php echo $product['unitName']; ?>)
								</td>
								<td class="text-right">
									<?php echo currency_format($product['item_price']); ?>
								</td>
								<td class="text-right">
									<?php echo currency_format($product['item_tax']); ?>
								</td>
								<td class="text-right">
									<?php echo currency_format($product['item_total']); ?>
								</td>
							</tr>
						<?php $inc++;endforeach; ?>
					</tbody>
					<tfoot>
						<tr>
							<td class="text-right" colspan="4">
								<?php echo trans('label_subtotal'); ?>
							</td>
							<td class="w-20 text-right">
								<?php echo currency_format($quotation['subtotal']+$quotation['item_tax']); ?>
							</td>
						</tr>
						<tr>
							<td class="text-right" colspan="4">
								<?php echo trans('label_order_tax'); ?> (+)
							</td>
							<td class="w-20 text-right">
								<?php echo currency_format($quotation['order_tax']); ?>
							</td>
						</tr>
						<tr>
							<td class="text-right" colspan="4">
								<?php echo trans('label_shipping'); ?> (+)
							</td>
							<td class="w-20 text-right">
								<?php echo currency_format($quotation['shipping_amount']); ?>
							</td>
						</tr>
						<tr>
							<td class="text-right" colspan="4">
								<?php echo trans('label_others_charge'); ?> (+)
							</td>
							<td class="w-20 text-right">
								<?php echo currency_format($quotation['others_charge']); ?>
							</td>
						</tr>
						<tr>
							<td class="text-right" colspan="4">
								<?php echo trans('label_discount'); ?> (-)
							</td>
							<td class="w-20 text-right">
								<?php echo currency_format($quotation['discount_amount']); ?>
							</td>
						</tr>
						<tr>
							<td class="text-right bold" colspan="4">
								<?php echo trans('label_payable_amount'); ?>
							</td>
							<td class="w-20 text-right bold">
								<?php echo currency_format($quotation['payable_amount']); ?>
							</td>
						</tr>
						<?php if ($quotation['quotation_note']):?>
						<tr>
							<td colspan="5"><b>Note: </b><?php echo $quotation['quotation_note'];?></td>
						</tr>
						<?php endif;?>
					</tfoot>
				</table>
			</div>

			<table class="table margin-b0 table-condensed mt-100">
				<tr>
					<td class="w-25 center b-0"></td>
					<td class="w-25 center b-0"></td>
					<td class="center b-0">
						<hr>
						<?php echo trans('label_stamp_and_signature'); ?>
					</td>
				</tr>
			</table>
				
		</div>
	</div>
</div>