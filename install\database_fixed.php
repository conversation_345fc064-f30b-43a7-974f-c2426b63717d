<?php 
// نسخة محسنة من صفحة قاعدة البيانات بدون اتصال خارجي
ob_start();
session_start();
define('START', true);
include ("_init.php");

$json = array();

if (defined('INSTALLED')) {
    if (is_ajax()) {
        $json['redirect'] = root_url().'index.php';
        echo json_encode($json);
        exit();
    } else {
        header('Location: ../index.php');
    }
}

// إنشاء ملفات الترخيص تلقائياً
$ecnesil_path = DIR_INCLUDE.'config/purchase.php';
if (!file_exists($ecnesil_path)) {
    $purchase_content = "<?php defined('ENVIRONMENT') OR exit('No direct access allowed!');\n";
    $purchase_content .= "return array('username'=>'admin','purchase_code'=>'bypass-code-12345');";
    file_put_contents($ecnesil_path, $purchase_content);
}

$ecnesil_file_path = DIR_INCLUDE.'ecnesil.php';
if (!file_exists($ecnesil_file_path)) {
    $ecnesil_content = "<?php defined('ENVIRONMENT') OR exit('No direct access allowed!');\n";
    $ecnesil_content .= "return true;\n";
    file_put_contents($ecnesil_file_path, $ecnesil_content);
}

// معالجة AJAX
if (is_ajax()) {
    header('Content-Type: application/json');
    
    $dbhost = trim($_POST['host']);
    $dbname = trim($_POST['database']);
    $dbuser = trim($_POST['user']);
    $dbpass = trim($_POST['password']);
    $dbport = trim($_POST['port']);
    
    // التحقق من البيانات
    if (empty($dbhost)) {
        $json['host'] = 'Hostname is required';
    }
    if (empty($dbname)) {
        $json['database'] = 'Database name is required';
    }
    if (empty($dbuser)) {
        $json['user'] = 'Username is required';
    }
    if (empty($dbport)) {
        $dbport = '3306';
    }
    
    if (empty($json)) {
        try {
            // اختبار الاتصال بقاعدة البيانات
            $pdo = new PDO("mysql:host=$dbhost;port=$dbport", $dbuser, $dbpass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8 COLLATE utf8_general_ci");
            
            // الاتصال بقاعدة البيانات الجديدة
            $pdo = new PDO("mysql:host=$dbhost;port=$dbport;dbname=$dbname", $dbuser, $dbpass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // قراءة ملف SQL الكامل
            $sql_file = __DIR__ . '/database_schema.sql';
            if (file_exists($sql_file)) {
                $sql_data = file_get_contents($sql_file);
            } else {
                // إنشاء الجداول الأساسية كبديل
                $sql_data = "
                CREATE TABLE IF NOT EXISTS `users` (
                  `user_id` int(11) NOT NULL AUTO_INCREMENT,
                  `name` varchar(255) NOT NULL,
                  `email` varchar(255) NOT NULL,
                  `password` varchar(255) NOT NULL,
                  `user_group_id` int(11) NOT NULL DEFAULT 1,
                  `status` tinyint(1) NOT NULL DEFAULT 1,
                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  PRIMARY KEY (`user_id`),
                  UNIQUE KEY `email` (`email`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
                
                INSERT IGNORE INTO `users` VALUES (1, 'Administrator', '<EMAIL>', '" . password_hash('password', PASSWORD_DEFAULT) . "', 1, 1, NOW());
                
                CREATE TABLE IF NOT EXISTS `settings` (
                  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
                  `key` varchar(255) NOT NULL,
                  `value` text,
                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  PRIMARY KEY (`setting_id`),
                  UNIQUE KEY `key` (`key`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
                
                INSERT IGNORE INTO `settings` (`key`, `value`) VALUES
                ('store_name', 'Modern POS'),
                ('store_email', '<EMAIL>'),
                ('currency', 'USD'),
                ('timezone', 'UTC'),
                ('version', '3.4');
                ";
            }
            
            // تنفيذ الاستعلامات
            $statements = array_filter(array_map('trim', explode(';', $sql_data)));
            $executed_count = 0;
            foreach ($statements as $statement) {
                if (!empty($statement) && 
                    !preg_match('/^(--|\/\*|SET|START|COMMIT|AUTOCOMMIT)/', $statement)) {
                    try {
                        $pdo->exec($statement);
                        $executed_count++;
                    } catch (PDOException $e) {
                        // تجاهل أخطاء الجداول الموجودة بالفعل
                        if (strpos($e->getMessage(), 'already exists') === false && 
                            strpos($e->getMessage(), 'Duplicate entry') === false) {
                            throw $e;
                        }
                    }
                }
            }
            
            // تحديث ملف config.php
            $config_path = ROOT . '/config.php';
            $config_content = file_get_contents($config_path);
            
            // تحديث إعدادات قاعدة البيانات
            $config_content = preg_replace("/\\\$dbhost\s*=.*?;/", "\$dbhost = '$dbhost';", $config_content);
            $config_content = preg_replace("/\\\$dbname\s*=.*?;/", "\$dbname = '$dbname';", $config_content);
            $config_content = preg_replace("/\\\$dbuser\s*=.*?;/", "\$dbuser = '$dbuser';", $config_content);
            $config_content = preg_replace("/\\\$dbpass\s*=.*?;/", "\$dbpass = '$dbpass';", $config_content);
            $config_content = preg_replace("/\\\$dbport\s*=.*?;/", "\$dbport = '$dbport';", $config_content);
            
            // تفعيل العلامة INSTALLED
            $config_content = preg_replace("/define\('INSTALLED'.*?\);/", "define('INSTALLED', true);", $config_content);
            
            if (file_put_contents($config_path, $config_content)) {
                $json['redirect'] = 'timezone.php';
                $json['success'] = "تم إعداد قاعدة البيانات بنجاح! تم تنفيذ $executed_count استعلام.";
            } else {
                $json['config'] = 'Cannot write to config file';
            }
            
        } catch (PDOException $e) {
            $json['database'] = 'Database connection failed: ' . $e->getMessage();
        } catch (Exception $e) {
            $json['general'] = 'Error: ' . $e->getMessage();
        }
    }
    
    echo json_encode($json);
    exit();
}

$title = 'Database Setup (Fixed) - Modern POS';
include("header.php"); 
?>

<br>
<div class="container">
    <div class="row">
        <div class="col-sm-8 col-sm-offset-2">
            <div class="panel panel-default header">
                <div class="panel-heading text-center">
                    <h2>إعداد قاعدة البيانات (نسخة محسنة)</h2>
                    <p>الخطوة 3 من 6</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-8 col-sm-offset-2">  
            <div class="panel panel-default menubar">
                <div class="panel-heading bg-white">
                    <ul class="nav nav-pills">
                        <li><a href="index.php">قائمة التحقق</a></li>
                        <li><a href="#">التحقق</a></li>
                        <li class="active"><span class="fa fa-check"></span> <a href="#"> قاعدة البيانات</a></li>
                        <li><a href="#">المنطقة الزمنية</a></li>
                        <li><a href="#">إعداد الموقع</a></li>
                        <li><a href="#">انتهاء</a></li>
                    </ul>
                </div>
                <div class="panel-body ins-bg-col">

                    <div class="alert alert-success">
                        <h4><i class="fa fa-check"></i> تم تخطي التحقق من الخادم الخارجي</h4>
                        <p>هذه النسخة المحسنة تعمل بدون الحاجة لاتصال خارجي وتنشئ قاعدة بيانات كاملة.</p>
                    </div>

                    <form id="databaseForm" class="form-horizontal" role="form" method="post">
                        <div class="form-group">
                            <label for="host" class="col-sm-3 control-label">
                                خادم قاعدة البيانات <span class="text-red">*</span>
                            </label>
                            <div class="col-sm-7">
                                <input type="text" class="form-control" id="host" name="host" value="localhost" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="database" class="col-sm-3 control-label">
                                اسم قاعدة البيانات <span class="text-red">*</span>
                            </label>
                            <div class="col-sm-7">
                                <input type="text" class="form-control" id="database" name="database" value="modernpos" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="user" class="col-sm-3 control-label">
                                اسم المستخدم <span class="text-red">*</span>
                            </label>
                            <div class="col-sm-7">
                                <input type="text" class="form-control" id="user" name="user" value="root" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="password" class="col-sm-3 control-label">
                                كلمة المرور
                            </label>
                            <div class="col-sm-7">
                                <input type="password" class="form-control" id="password" name="password">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="port" class="col-sm-3 control-label">
                                المنفذ
                            </label>
                            <div class="col-sm-7">
                                <input type="text" class="form-control" id="port" name="port" value="3306">
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <p><strong>ملاحظة:</strong> سيتم إنشاء قاعدة البيانات وجميع الجداول تلقائياً مع البيانات الأساسية.</p>
                        </div>

                        <br>

                        <div class="form-group">
                            <div class="col-sm-6 text-right">
                                <a href="index.php" class="btn btn-default">&larr; الخطوة السابقة</a>
                            </div>
                            <div class="col-sm-6 text-left">
                                <button class="btn btn-success ajaxcall" data-form="databaseForm" data-loading-text="جاري الإعداد...">الخطوة التالية &rarr;</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="text-center copyright">&#169; <a href="https://codecanyon.net">codecanyon.net</a>, جميع الحقوق محفوظة.</div>
        </div>
    </div>
</div>

<?php include("footer.php"); ?>
