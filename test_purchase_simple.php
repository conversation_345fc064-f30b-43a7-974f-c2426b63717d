<?php 
// اختبار مبسط لصفحة purchase_code
ob_start();
session_start();
define('START', true);
include ("install/_init.php");

$json = array();

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    header('Content-Type: application/json');
    
    // تسجيل البيانات المستلمة
    error_log("POST data received: " . print_r($_POST, true));
    
    try {
        // إنشاء ملف الترخيص مباشرة
        $username = !empty($_POST['purchase_username']) ? $_POST['purchase_username'] : 'admin';
        $purchase_code = !empty($_POST['purchase_code']) ? $_POST['purchase_code'] : 'bypass-code-12345';
        
        $ecnesil_path = DIR_INCLUDE.'config/purchase.php';
        
        // إنشاء محتوى ملف الترخيص
        $purchase_content = "<?php defined('ENVIRONMENT') OR exit('No direct access allowed!');\n";
        $purchase_content .= "return array('username'=>'".addslashes($username)."','purchase_code'=>'".addslashes($purchase_code)."');";
        
        // كتابة الملف
        if (file_put_contents($ecnesil_path, $purchase_content) !== false) {
            error_log("License file created successfully");
            $json['redirect'] = 'database.php';
        } else {
            error_log("Failed to create license file");
            $json['config_error'] = 'Could not write license file!';
        }
    } catch (Exception $e) {
        error_log("Exception: " . $e->getMessage());
        $json['preparation'] = 'Error: ' . $e->getMessage();
    }
    
    echo json_encode($json);
    exit();
}

$title = 'Test Purchase Code Validation';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><?php echo $title; ?></title>
    <link type="text/css" href="assets/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <script src="assets/jquery/jquery.min.js"></script>
    <script src="assets/bootstrap/js/bootstrap.min.js"></script>
</head>
<body>
<div class="container">
    <div class="row">
        <div class="col-md-6 col-md-offset-3">
            <h2>اختبار تخطي التحقق من رمز الشراء</h2>
            
            <div class="alert alert-info">
                <p>هذا اختبار مبسط لتخطي التحقق من رمز الشراء</p>
            </div>
            
            <form id="testForm" method="post">
                <div class="form-group">
                    <label>اسم المستخدم:</label>
                    <input type="text" class="form-control" name="purchase_username" value="admin" placeholder="admin">
                </div>
                
                <div class="form-group">
                    <label>رمز الشراء:</label>
                    <input type="text" class="form-control" name="purchase_code" value="bypass-code-12345" placeholder="bypass-code-12345">
                </div>
                
                <button type="submit" class="btn btn-success">اختبار التخطي</button>
                <a href="install/" class="btn btn-default">العودة للتثبيت</a>
            </form>
            
            <div id="result" style="margin-top: 20px;"></div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#testForm').on('submit', function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        console.log('Sending data:', formData);
        
        $.ajax({
            url: 'test_purchase_simple.php',
            type: 'POST',
            dataType: 'json',
            data: formData,
            success: function(response) {
                console.log('Response:', response);
                
                if (response.redirect) {
                    $('#result').html('<div class="alert alert-success">✅ نجح! سيتم التوجيه إلى: ' + response.redirect + '</div>');
                    setTimeout(function() {
                        window.location = 'install/' + response.redirect;
                    }, 2000);
                } else {
                    var errors = '';
                    $.each(response, function(key, value) {
                        errors += '<p>❌ ' + key + ': ' + value + '</p>';
                    });
                    $('#result').html('<div class="alert alert-danger">' + errors + '</div>');
                }
            },
            error: function(xhr, status, error) {
                console.log('Error:', error);
                console.log('Response:', xhr.responseText);
                $('#result').html('<div class="alert alert-danger">❌ خطأ: ' + error + '<br>Response: ' + xhr.responseText + '</div>');
            }
        });
    });
});
</script>
</body>
</html>
