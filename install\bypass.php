<?php 
// صفحة تخطي التحقق من رمز الشراء - حل مباشر
ob_start();
session_start();
define('START', true);
include ("_init.php");

// إنشاء ملف الترخيص مباشرة
function create_license_file() {
    $ecnesil_path = DIR_INCLUDE.'config/purchase.php';
    
    // إنشاء محتوى ملف الترخيص
    $purchase_content = "<?php defined('ENVIRONMENT') OR exit('No direct access allowed!');\n";
    $purchase_content .= "return array('username'=>'admin','purchase_code'=>'bypass-code-12345');";
    
    // كتابة الملف
    if (file_put_contents($ecnesil_path, $purchase_content) !== false) {
        return true;
    }
    return false;
}

// إنشاء ملف ecnesil.php
function create_ecnesil_file() {
    $ecnesil_file_path = DIR_INCLUDE.'ecnesil.php';
    $ecnesil_content = "<?php defined('ENVIRONMENT') OR exit('No direct access allowed!');\n";
    $ecnesil_content .= "// ملف الترخيص المحلي\n";
    $ecnesil_content .= "return true;\n";
    
    if (file_put_contents($ecnesil_file_path, $ecnesil_content) !== false) {
        return true;
    }
    return false;
}

$message = '';
$success = false;

// إذا تم الضغط على زر التخطي
if (isset($_GET['action']) && $_GET['action'] == 'bypass') {
    if (create_license_file() && create_ecnesil_file()) {
        $message = 'تم تخطي التحقق من رمز الشراء بنجاح!';
        $success = true;
    } else {
        $message = 'فشل في إنشاء ملفات الترخيص. تحقق من صلاحيات الكتابة.';
    }
}

$title = 'تخطي التحقق من رمز الشراء - Modern POS';
include("header.php"); 
?>

<br>
<div class="container">
    <div class="row">
        <div class="col-sm-8 col-sm-offset-2">
            <div class="panel panel-default header">
                <div class="panel-heading text-center">
                    <h2>تخطي التحقق من رمز الشراء</h2>
                    <p>حل مباشر لتجاوز مشكلة التحقق</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-8 col-sm-offset-2">  
            <div class="panel panel-default menubar">
                <div class="panel-heading bg-white">
                    <ul class="nav nav-pills">
                        <li>
                            <a href="index.php">قائمة التحقق</a>
                        </li>
                        <li class="active">
                            <span class="fa fa-check"></span> <a href="#"> تخطي التحقق</a>
                        </li>
                        <li>
                            <a href="database.php">قاعدة البيانات</a>
                        </li>
                        <li>
                            <a href="timezone.php">المنطقة الزمنية</a>
                        </li>
                        <li>
                            <a href="site.php">إعداد الموقع</a>
                        </li>
                        <li>
                            <a href="done.php">انتهاء</a>
                        </li>
                    </ul>
                </div>
                <div class="panel-body ins-bg-col">

                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $success ? 'success' : 'danger'; ?>">
                            <h4><i class="fa fa-<?php echo $success ? 'check' : 'exclamation-triangle'; ?>"></i> <?php echo $success ? 'نجح!' : 'خطأ!'; ?></h4>
                            <p><?php echo $message; ?></p>
                        </div>
                    <?php endif; ?>

                    <div class="alert alert-info">
                        <h4><i class="fa fa-info-circle"></i> معلومات مهمة</h4>
                        <p>هذه الصفحة تتيح لك تخطي التحقق من رمز الشراء بالكامل وإنشاء ملفات الترخيص محلياً.</p>
                        <p><strong>ملاحظة:</strong> هذا للاستخدام التعليمي والتطويري فقط.</p>
                    </div>

                    <div class="well">
                        <h4>معلومات النظام:</h4>
                        <ul>
                            <li><strong>مجلد التضمين:</strong> <?php echo DIR_INCLUDE; ?></li>
                            <li><strong>ملف الترخيص:</strong> <?php echo DIR_INCLUDE.'config/purchase.php'; ?></li>
                            <li><strong>صلاحية الكتابة:</strong> 
                                <?php if (is_writable(DIR_INCLUDE.'config/')): ?>
                                    <span class="text-success">✓ متاحة</span>
                                <?php else: ?>
                                    <span class="text-danger">✗ غير متاحة</span>
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>

                    <?php if ($success): ?>
                        <div class="form-group">
                            <div class="col-sm-6 text-right">
                                <a href="index.php" class="btn btn-default">&larr; الخطوة السابقة</a>
                            </div>
                            <div class="col-sm-6 text-left">
                                <a href="database.php" class="btn btn-success">الخطوة التالية &rarr;</a>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="form-group">
                            <div class="col-sm-6 text-right">
                                <a href="index.php" class="btn btn-default">&larr; الخطوة السابقة</a>
                            </div>
                            <div class="col-sm-6 text-left">
                                <a href="bypass.php?action=bypass" class="btn btn-primary">تخطي التحقق الآن &rarr;</a>
                            </div>
                        </div>
                    <?php endif; ?>

                </div>
            </div>
            <div class="text-center copyright">&#169; <a href="https://codecanyon.net">codecanyon.net</a>, جميع الحقوق محفوظة.</div>
        </div>
    </div>
</div>

<?php include("footer.php"); ?>
