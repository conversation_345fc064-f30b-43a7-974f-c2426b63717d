<h4 class="sub-title">
  <?php echo trans('text_update_title'); ?>
</h4>

<form class="form-horizontal" id="printer-form" action="printer.php" method="post">
  <input type="hidden" id="action_type" name="action_type" value="UPDATE">
  <input type="hidden" id="printer_id" name="printer_id" value="<?php echo $printer['printer_id']; ?>">
  <div class="box-body">
    
      <div class="form-group">
        <label for="title" class="col-sm-3 control-label">
          <?php echo trans('label_title'); ?><i class="required">*</i>
        </label>
        <div class="col-sm-8">
          <input type="text" class="form-control" id="title" value="<?php echo isset($printer['title']) ? $printer['title'] : null; ?>" name="title" required>
        </div>
      </div>  

      <div class="form-group">
        <label for="type" class="col-sm-3 control-label">
          <?php echo trans('label_type'); ?><i class="required">*</i>
        </label>
        <div class="col-sm-8">
          <select class="form-control select2"  name="type" id="edit-printer-type" ng-init="editPrinterType='<?php echo $printer['type'];?>'" ng-model="editPrinterType">
            <option value="network">Network</option>
            <option value="windows">Windows</option>
            <option value="linux">Linux</option>
         </select>
        </div>
      </div>

      <div class="form-group">
        <label for="char_per_line" class="col-sm-3 control-label">
          <?php echo trans('label_char_per_line'); ?><i class="required">*</i>
        </label>
        <div class="col-sm-8">
          <input type="text" class="form-control" id="char_per_line" value="<?php echo isset($printer['char_per_line']) ? $printer['char_per_line'] : 200; ?>" name="char_per_line" required>
        </div>
      </div> 

      <div ng-show="!isEditNetwork" class="form-group" ng-init="isEditWindows='<?php echo $printer['type'] != 'network';?>'">
        <label for="path" class="col-sm-3 control-label">
          <?php echo trans('label_path'); ?>
        </label>
        <div class="col-sm-8">
          <input type="text" class="form-control" id="path" value="<?php echo isset($printer['path']) ? $printer['path'] : null; ?>" name="path" required>
          <p>
            <small>
              <b>For Windows:</b><?php echo trans('text_windows_instruction'); ?><span class="text-blue">smb://computername/Receipt Printer</span> <br>
              <b>For Linux:</b> Parallel as <span class="text-blue">/dev/lp0</span>, USB as <span class="text-blue">/dev/usb/lp1</span>, USB-Serial as <span class="text-blue">/dev/ttyUSB0</span>, Serial as <span class="text-blue">/dev/ttyS0</span>
            </small>
          </p>
        </div>
      </div> 

      <div ng-show="isEditNetwork" class="form-group" ng-init="isEditNetwork='<?php echo $printer['type'] == 'network';?>'">
        <label for="ip_address" class="col-sm-3 control-label">
          <?php echo trans('label_ip_address'); ?>
        </label>
        <div class="col-sm-8">
          <input type="text" class="form-control" id="ip_address" value="<?php echo isset($printer['ip_address']) ? $printer['ip_address'] : null; ?>" name="ip_address" required>
        </div>
      </div> 

      <div ng-show="isEditNetwork" class="form-group">
        <label for="port" class="col-sm-3 control-label">
          <?php echo trans('label_port'); ?>
        </label>
        <div class="col-sm-8">
          <input type="text" class="form-control" id="port" value="<?php echo isset($printer['port']) ? $printer['port'] : 9100; ?>" name="port" required>
          <p>
              <i><?php echo trans('text_printer_port'); ?></i>
          </p>
        </div>
      </div> 

      <div class="form-group">
      <label  for="store_select" class="col-sm-3 control-label">
        <?php echo trans('label_store'); ?><i class="required">*</i>
      </label>
      <div class="col-sm-8 store-selector">
        <div class="checkbox selector">
          <label>
            <input id="store_select" type="checkbox" onclick="$('input[name*=\'printer_store\']').prop('checked', this.checked);"> Select / Deselect
          </label>
        </div>
        <div class="filter-searchbox">
          <input ng-model="search_store" class="form-control" type="text" id="search_store" placeholder="<?php echo trans('search'); ?>">
        </div>
        <div class="well well-sm store-well"> 
          <div filter-list="search_store">
          <?php foreach(get_stores() as $the_store) : ?>                    
            <div class="checkbox">
              <label>                         
                <input type="checkbox" name="printer_store[]" value="<?php echo $the_store['store_id']; ?>" <?php echo $the_store['store_id'] == store_id() ? 'checked' : null; ?>>
                <?php echo $the_store['name']; ?>
              </label>
            </div>
          <?php endforeach; ?>
          </div>
        </div>
      </div>
    </div>

    <div class="form-group">
      <label for="status" class="col-sm-3 control-label">
        <?php echo trans('label_status'); ?><i class="required">*</i>
      </label>
      <div class="col-sm-8">
        <select id="status" class="form-control" name="status" required>
          <option value="1">
            <?php echo trans('text_active'); ?>
          </option>
          <option value="0">
            <?php echo trans('text_inactive'); ?>
          </option>
        </select>
      </div>
    </div>

    <div class="form-group">
      <label for="sort_order" class="col-sm-3 control-label">
        <?php echo trans('label_sort_order'); ?><i class="required">*</i>
      </label>
      <div class="col-sm-8">
        <input type="number" class="form-control" id="sort_order" value="<?php echo isset($printer['sort_order']) ? $printer['sort_order'] : 0; ?>" name="sort_order">
      </div>
    </div>

      <div class="form-group">
        <label class="col-sm-3 control-label"></label>
        <div class="col-sm-8">
          <button id="printer-update" data-form="#printer-form" data-datatable="#printer-printer-list" class="btn btn-info" name="btn_edit_printer" data-loading-text="Updating...">
            <span class="fa fa-fw fa-pencil"></span>
            <?php echo trans('button_update'); ?>
          </button>
        </div>
      </div>

  </div>
</form>