<?php 
// تثبيت كامل بدون اتصال خارجي
ob_start();
session_start();
define('START', true);
include ("_init.php");

// تحديد الخطوة الحالية
$step = 1;
if (isset($_GET['step'])) {
    $step = (int)$_GET['step'];
} elseif (isset($_POST['step'])) {
    $step = (int)$_POST['step'];
} elseif (isset($_SESSION['install_step'])) {
    $step = $_SESSION['install_step'];
}

$message = '';
$error = '';
$success = false;

// التحقق من وجود ملفات الترخيص للانتقال للخطوة 2
$ecnesil_path = DIR_INCLUDE.'config/purchase.php';
if (file_exists($ecnesil_path) && $step == 1) {
    $step = 2;
    $_SESSION['install_step'] = 2;
}

// الخطوة 1: إنشاء ملفات الترخيص
if ($step == 1) {
    if (isset($_POST['create_license'])) {
        // إنشاء ملف purchase.php
        $ecnesil_path = DIR_INCLUDE.'config/purchase.php';
        $purchase_content = "<?php defined('ENVIRONMENT') OR exit('No direct access allowed!');\n";
        $purchase_content .= "return array('username'=>'admin','purchase_code'=>'bypass-code-12345');";
        
        // إنشاء ملف ecnesil.php
        $ecnesil_file_path = DIR_INCLUDE.'ecnesil.php';
        $ecnesil_content = "<?php defined('ENVIRONMENT') OR exit('No direct access allowed!');\n";
        $ecnesil_content .= "return true;\n";
        
        if (file_put_contents($ecnesil_path, $purchase_content) &&
            file_put_contents($ecnesil_file_path, $ecnesil_content)) {
            $message = 'تم إنشاء ملفات الترخيص بنجاح!';
            $step = 2;
            $_SESSION['install_step'] = 2;
            $success = true;
        } else {
            $error = 'فشل في إنشاء ملفات الترخيص. تحقق من صلاحيات الكتابة.';
        }
    }
}

// الخطوة 2: إعداد قاعدة البيانات
if ($step == 2 && isset($_POST['setup_database'])) {
    $host = trim($_POST['host']);
    $database = trim($_POST['database']);
    $username = trim($_POST['username']);
    $password = trim($_POST['password']);
    $port = trim($_POST['port']);

    // تسجيل محاولة الإعداد
    error_log("Database setup attempt: host=$host, database=$database, username=$username, port=$port");

    try {
        // اختبار الاتصال
        $pdo = new PDO("mysql:host=$host;port=$port", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        error_log("Database connection successful");

        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8 COLLATE utf8_general_ci");
        error_log("Database created/verified: $database");

        // الاتصال بقاعدة البيانات الجديدة
        $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // إنشاء الجداول الأساسية مباشرة
        $tables_created = 0;

        // جدول المستخدمين
        $pdo->exec("CREATE TABLE IF NOT EXISTS `users` (
            `user_id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `email` varchar(255) NOT NULL,
            `password` varchar(255) NOT NULL,
            `user_group_id` int(11) NOT NULL DEFAULT 1,
            `status` tinyint(1) NOT NULL DEFAULT 1,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`user_id`),
            UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8");
        $tables_created++;

        // إدراج المستخدم الافتراضي
        $pdo->exec("INSERT IGNORE INTO `users` (`user_id`, `name`, `email`, `password`, `user_group_id`, `status`) VALUES
        (1, 'Administrator', '<EMAIL>', '" . password_hash('password', PASSWORD_DEFAULT) . "', 1, 1)");

        // جدول المجموعات
        $pdo->exec("CREATE TABLE IF NOT EXISTS `user_groups` (
            `user_group_id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `permissions` text,
            `status` tinyint(1) NOT NULL DEFAULT 1,
            PRIMARY KEY (`user_group_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8");
        $tables_created++;

        // إدراج المجموعات الافتراضية
        $pdo->exec("INSERT IGNORE INTO `user_groups` (`user_group_id`, `name`, `permissions`, `status`) VALUES
        (1, 'Administrator', '{\"access\":{\"dashboard\":1,\"pos\":1,\"products\":1,\"customers\":1,\"suppliers\":1,\"reports\":1,\"settings\":1}}', 1)");

        // جدول الإعدادات
        $pdo->exec("CREATE TABLE IF NOT EXISTS `settings` (
            `setting_id` int(11) NOT NULL AUTO_INCREMENT,
            `key` varchar(255) NOT NULL,
            `value` text,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`setting_id`),
            UNIQUE KEY `key` (`key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8");
        $tables_created++;

        // إدراج الإعدادات الافتراضية
        $pdo->exec("INSERT IGNORE INTO `settings` (`key`, `value`) VALUES
        ('store_name', 'Modern POS'),
        ('store_email', '<EMAIL>'),
        ('currency', 'USD'),
        ('timezone', 'UTC'),
        ('version', '3.4')");

        error_log("Tables created: $tables_created");

        // تحديث ملف config.php
        $config_path = ROOT . '/config.php';
        $config_content = file_get_contents($config_path);

        $config_content = preg_replace("/\\\$dbhost\s*=.*?;/", "\$dbhost = '$host';", $config_content);
        $config_content = preg_replace("/\\\$dbname\s*=.*?;/", "\$dbname = '$database';", $config_content);
        $config_content = preg_replace("/\\\$dbuser\s*=.*?;/", "\$dbuser = '$username';", $config_content);
        $config_content = preg_replace("/\\\$dbpass\s*=.*?;/", "\$dbpass = '$password';", $config_content);
        $config_content = preg_replace("/\\\$dbport\s*=.*?;/", "\$dbport = '$port';", $config_content);
        $config_content = preg_replace("/define\('INSTALLED'.*?\);/", "define('INSTALLED', true);", $config_content);

        if (file_put_contents($config_path, $config_content)) {
            error_log("Config file updated successfully");
            $message = "تم إعداد قاعدة البيانات وتثبيت النظام بنجاح! تم إنشاء $tables_created جداول.";
            $step = 3;
            $_SESSION['install_step'] = 3;
            $_SESSION['install_complete'] = true;
            $success = true;
        } else {
            $error = 'فشل في تحديث ملف الإعدادات.';
            error_log("Failed to update config file");
        }
    } catch (Exception $e) {
        $error = 'خطأ في إعداد قاعدة البيانات: ' . $e->getMessage();
        error_log("Database setup error: " . $e->getMessage());
    }
}

$title = 'التثبيت الكامل بدون اتصال خارجي - Modern POS';
include("header.php"); 
?>

<br>
<div class="container">
    <div class="row">
        <div class="col-sm-10 col-sm-offset-1">
            <div class="panel panel-default header">
                <div class="panel-heading text-center">
                    <h2>التثبيت الكامل بدون اتصال خارجي</h2>
                    <p>حل نهائي لجميع مشاكل التثبيت</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-sm-10 col-sm-offset-1">  
            <div class="panel panel-default">
                <div class="panel-body">
                    
                    <!-- Progress Bar -->
                    <div class="progress" style="margin-bottom: 20px;">
                        <div class="progress-bar progress-bar-success" style="width: <?php echo ($step * 33.33); ?>%">
                            الخطوة <?php echo $step; ?> من 3
                        </div>
                    </div>
                    
                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $success ? 'success' : 'info'; ?>">
                            <i class="fa fa-<?php echo $success ? 'check' : 'info'; ?>"></i> <?php echo $message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-triangle"></i> <?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($step == 1): ?>
                        <div class="row">
                            <div class="col-md-8">
                                <h3>الخطوة 1: إنشاء ملفات الترخيص</h3>
                                <p>سيتم إنشاء ملفات الترخيص المطلوبة تلقائياً بدون الحاجة لاتصال خارجي.</p>
                                
                                <div class="well">
                                    <h4>ما سيتم إنشاؤه:</h4>
                                    <ul>
                                        <li>ملف الترخيص الأساسي</li>
                                        <li>ملف التحقق المحلي</li>
                                        <li>إعدادات النظام الافتراضية</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="panel panel-info">
                                    <div class="panel-heading">معلومات النظام</div>
                                    <div class="panel-body">
                                        <p><strong>PHP:</strong> <?php echo phpversion(); ?></p>
                                        <p><strong>صلاحية الكتابة:</strong> 
                                            <?php if (is_writable(DIR_INCLUDE.'config/')): ?>
                                                <span class="text-success">✓</span>
                                            <?php else: ?>
                                                <span class="text-danger">✗</span>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <form method="post">
                            <input type="hidden" name="step" value="1">
                            <button type="submit" name="create_license" class="btn btn-primary btn-lg btn-block">
                                <i class="fa fa-rocket"></i> بدء التثبيت - إنشاء ملفات الترخيص
                            </button>
                        </form>
                        
                    <?php elseif ($step == 2): ?>
                        <h3>الخطوة 2: إعداد قاعدة البيانات</h3>
                        <p>أدخل بيانات قاعدة البيانات. سيتم إنشاء قاعدة البيانات تلقائياً إذا لم تكن موجودة.</p>
                        
                        <form method="post" class="form-horizontal">
                            <input type="hidden" name="step" value="2">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">خادم قاعدة البيانات:</label>
                                <div class="col-sm-9">
                                    <input type="text" name="host" class="form-control" value="localhost" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-sm-3 control-label">اسم قاعدة البيانات:</label>
                                <div class="col-sm-9">
                                    <input type="text" name="database" class="form-control" value="modernpos" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-sm-3 control-label">اسم المستخدم:</label>
                                <div class="col-sm-9">
                                    <input type="text" name="username" class="form-control" value="root" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-sm-3 control-label">كلمة المرور:</label>
                                <div class="col-sm-9">
                                    <input type="password" name="password" class="form-control">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-sm-3 control-label">المنفذ:</label>
                                <div class="col-sm-9">
                                    <input type="text" name="port" class="form-control" value="3306" required>
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <h4><i class="fa fa-info-circle"></i> ملاحظة مهمة</h4>
                                <p>سيتم إنشاء قاعدة البيانات وجميع الجداول تلقائياً مع البيانات الأساسية.</p>
                            </div>
                            
                            <button type="submit" name="setup_database" class="btn btn-success btn-lg btn-block">
                                <i class="fa fa-database"></i> إعداد قاعدة البيانات وإنهاء التثبيت
                            </button>
                        </form>
                        
                    <?php elseif ($step == 3): ?>
                        <div class="text-center">
                            <h3><i class="fa fa-check-circle text-success" style="font-size: 4em;"></i></h3>
                            <h2>تم التثبيت بنجاح!</h2>
                            
                            <div class="alert alert-success">
                                <h4>تم إنجاز جميع الخطوات بنجاح</h4>
                                <ul class="list-unstyled">
                                    <li>✓ تم إنشاء ملفات الترخيص</li>
                                    <li>✓ تم إعداد قاعدة البيانات</li>
                                    <li>✓ تم تثبيت النظام</li>
                                </ul>
                            </div>
                            
                            <div class="well">
                                <h4>بيانات الدخول الافتراضية:</h4>
                                <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                                <p><strong>كلمة المرور:</strong> password</p>
                            </div>
                            
                            <a href="../index.php" class="btn btn-success btn-lg">
                                <i class="fa fa-sign-in"></i> الدخول إلى النظام
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <hr>
                    <div class="text-center">
                        <small class="text-muted">
                            تم التثبيت بدون اتصال خارجي | Modern POS Offline Installation
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include("footer.php"); ?>
